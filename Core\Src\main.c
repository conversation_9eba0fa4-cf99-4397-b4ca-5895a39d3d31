/* USER CODE BEGIN Header */
/**
  ******************************************************************************
  * @file           : main.c
  * @brief          : Main program body
  ******************************************************************************
  * @attention
  *
  * Copyright (c) 2025 STMicroelectronics.
  * All rights reserved.
  *
  * This software is licensed under terms that can be found in the LICENSE file
  * in the root directory of this software component.
  * If no LICENSE file comes with this software, it is provided AS-IS.
  *
  ******************************************************************************
  */
/* USER CODE END Header */
/* Includes ------------------------------------------------------------------*/
#include "main.h"
#include "cmsis_os.h"
#include "adc.h"
#include "dma.h"
#include "i2c.h"
#include "rtc.h"
#include "spi.h"
#include "usart.h"
#include "gpio.h"
#include <stdio.h>
#include <string.h>

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include "GM20.h"
#include "GPS.h"
#include "lsm6ds3.h"
#include "rtc_sync.h"
/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/

/* USER CODE BEGIN PV */
// ADC数据存储数组，3个通道，每个通道20个采样
uint32_t ADC_Value[60];  // 3通道 × 20采样 = 60个数据
/* USER CODE END PV */

/* Private function prototypes -----------------------------------------------*/
void SystemClock_Config(void);
void MX_FREERTOS_Init(void);
/* USER CODE BEGIN PFP */
void ADC_Test_And_Print(void);
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
HAL_StatusTypeDef Send_Data_To_GM20(const char *data_string);
/* USER CODE END PFP */

/* Private user code ---------------------------------------------------------*/
/* USER CODE BEGIN 0 */

/**
  * @brief  重定向printf到USART1
  * @param  ch: 要发送的字符
  * @retval 发送的字符
  */
int fputc(int ch, FILE *f)
{
    HAL_UART_Transmit(&huart1, (uint8_t *)&ch, 1, HAL_MAX_DELAY);
    return ch;
}

/**
  * @brief  ADC测试和数据打印函数
  * @retval None
  */
void ADC_Test_And_Print(void)
{
    printf("\r\n=== ADC测试开始 ===\r\n");

    // 打开ADC电源
    ADC_ON;
    HAL_Delay(10);  // 等待电源稳定

    // 启动ADC DMA传输，3个通道，每个通道20个采样，总共60个数据
    if (HAL_ADC_Start_DMA(&hadc, ADC_Value, 60) == HAL_OK) {
        printf("ADC DMA启动成功\r\n");
        HAL_Delay(100);  // 等待采样完成

        // 打印所有原始ADC数据
        printf("原始ADC数据 (60个数据):\r\n");
        for(int i = 0; i < 60; i++) {
            printf("%4lu ", ADC_Value[i]);
            if((i + 1) % 10 == 0) {  // 每10个数据换行
                printf("\r\n");
            }
        }
        printf("\r\n");

        // 分析通道数据分布
        printf("\r\n=== 通道数据分析 ===\r\n");
        printf("根据ADC配置，通道顺序应该是：\r\n");
        printf("1. ADC_CHANNEL_6 (PA6) - 外部电压\r\n");
        printf("2. ADC_CHANNEL_TEMPSENSOR - 内部温度传感器\r\n");
        printf("3. ADC_CHANNEL_VREFINT - 内部参考电压\r\n");

        // 分离各通道数据并计算平均值
        uint32_t sum_ch6 = 0, sum_temp = 0, sum_vref = 0;
        int count_ch6 = 0, count_temp = 0, count_vref = 0;

        printf("\r\n各通道数据分布:\r\n");
        printf("CH6数据: ");
        for(int i = 0; i < 60; i += 3) {
            printf("%lu ", ADC_Value[i]);
            sum_ch6 += ADC_Value[i];
            count_ch6++;
        }
        printf("\r\n");

        printf("TEMP数据: ");
        for(int i = 1; i < 60; i += 3) {
            printf("%lu ", ADC_Value[i]);
            sum_temp += ADC_Value[i];
            count_temp++;
        }
        printf("\r\n");

        printf("VREF数据: ");
        for(int i = 2; i < 60; i += 3) {
            printf("%lu ", ADC_Value[i]);
            sum_vref += ADC_Value[i];
            count_vref++;
        }
        printf("\r\n");

        // 计算并打印平均值
        printf("\r\n=== 平均值统计 ===\r\n");
        if(count_ch6 > 0) {
            printf("CH6平均值: %lu (采样%d次)\r\n", sum_ch6/count_ch6, count_ch6);
        }
        if(count_temp > 0) {
            printf("TEMP平均值: %lu (采样%d次)\r\n", sum_temp/count_temp, count_temp);
        }
        if(count_vref > 0) {
            printf("VREF平均值: %lu (采样%d次)\r\n", sum_vref/count_vref, count_vref);
        }

        // 停止ADC DMA传输
        HAL_ADC_Stop_DMA(&hadc);
        printf("ADC DMA已停止\r\n");
    } else {
        printf("ADC DMA启动失败!\r\n");
    }

    // 关闭ADC电源
    ADC_OFF;
    printf("=== ADC测试结束 ===\r\n\r\n");
}

/**
  * @brief  创建完整的数据字符串用于传输
  * @param  output_buffer: 输出缓冲区
  * @param  buffer_size: 缓冲区大小
  * @retval HAL状态
  */
HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size)
{
    printf("Create_Data_String: Starting data creation...\r\n");

    extern GPS_Data_t gps_data;
    extern float pw;
    extern LSM6DS3_Data imuData;
    extern LSM6DS3_Attitude attitude;

    char time_str[20] = {0};
    char sn_buffer[32] = {0};
    int8_t signal_quality = -128;

    // Get GM20 module information
    printf("Create_Data_String: Getting GM20 SN...\r\n");
    if (GM20_GetSN(sn_buffer) != GM20_OK) {
        strcpy(sn_buffer, "00000000");
    }
    printf("Create_Data_String: SN = %s\r\n", sn_buffer);

    printf("Create_Data_String: Getting signal quality...\r\n");
    if (GM20_GetSignalQuality(&signal_quality) != GM20_OK) {
        signal_quality = -128;
    }
    printf("Create_Data_String: Signal quality = %d\r\n", signal_quality);

    // Smart time source selection logic
    uint8_t gps_time_valid = (gps_data.valid && gps_data.hour <= 23 &&
                             gps_data.minute <= 59 && gps_data.second <= 59);
    uint8_t gps_date_valid = (gps_data.year >= 2020 && gps_data.month >= 1 &&
                             gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31);

    // Get RTC time as primary source when GPS invalid
    printf("Create_Data_String: Getting RTC time...\r\n");
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    RTC_GetDateTime(&rtc_time, &rtc_date);
    printf("Create_Data_String: RTC time obtained\r\n");

    // Time source priority: GPS valid and has coordinates -> use GPS time, otherwise use RTC
    if (gps_data.valid && gps_data.latitude > 0 && gps_data.longitude > 0) {
        if (gps_time_valid && gps_date_valid) {
            // Use complete GPS time when GPS has valid positioning
            snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                    gps_data.hour, gps_data.minute, gps_data.second,
                    gps_data.day, gps_data.month, gps_data.year % 100);
        } else if (gps_time_valid && !gps_date_valid) {
            // Use GPS time + RTC date when GPS time valid but date invalid
            snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                    gps_data.hour, gps_data.minute, gps_data.second,
                    rtc_date.Date, rtc_date.Month, rtc_date.Year);
        } else {
            // GPS positioning valid but time invalid, use RTC time
            snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                    rtc_time.Hours, rtc_time.Minutes, rtc_time.Seconds,
                    rtc_date.Date, rtc_date.Month, rtc_date.Year);
        }
    } else {
        // GPS invalid or no positioning, always use RTC time and date
        snprintf(time_str, sizeof(time_str), "%02d%02d%02d.%02d%02d%02d",
                rtc_time.Hours, rtc_time.Minutes, rtc_time.Seconds,
                rtc_date.Date, rtc_date.Month, rtc_date.Year);
    }

    // GPS data or default values
    float longitude = gps_data.longitude;
    float latitude = gps_data.latitude;
    float altitude = gps_data.altitude;
    uint8_t fix_quality = gps_data.fix_quality;
    uint8_t satellites = gps_data.satellites;
    float speed = gps_data.speed;
    float hdop = gps_data.hdop;
    float pdop = gps_data.pdop;
    float course = gps_data.course;

    if (!gps_data.valid) {
        // Use default values when GPS is invalid
        longitude = 0.0;
        latitude = 0.0;
        altitude = 0.0;
        fix_quality = 0;
        satellites = 0;
        speed = 0.0;
        hdop = 99.9;
        pdop = 99.9;
        course = 0.0;
    } else {
        // GPS valid but check for missing precision data
        if (hdop <= 0.0) {
            hdop = 2.5;  // Use reasonable default when HDOP not available
        }
        if (pdop <= 0.0 || pdop >= 99.0) {
            pdop = 3.0;  // Use reasonable default when PDOP not available or invalid
        }
    }

    // Create data content string
    char data_content[200];
    snprintf(data_content, sizeof(data_content),
            "S+%.5f+%.5f+%s+%.1f+%d+%d+%.1f+%.2f+%d+%.1f+%.1f+%.1f+%.1f+%.1f+%.2f+%d+%s+E",
            longitude,
            latitude,
            time_str,
            altitude,
            fix_quality,
            satellites,
            speed,
            pw,
            (int)imuData.temp_celsius,
            hdop,
            pdop,
            attitude.roll,
            attitude.pitch,
            attitude.yaw,
            course,
            signal_quality,
            sn_buffer
            );

    // Build complete string with length header
    printf("Create_Data_String: Building final packet...\r\n");
    uint16_t data_length = strlen(data_content);
    if (data_length < 100) {
        snprintf(output_buffer, buffer_size, "HY0%d%s", data_length, data_content);
    } else {
        snprintf(output_buffer, buffer_size, "HY%d%s", data_length, data_content);
    }
    printf("Create_Data_String: Packet created successfully\r\n");

    return HAL_OK;
}

/**
  * @brief  发送数据到GM20模块
  * @param  data_string: 要发送的数据字符串
  * @retval HAL状态
  */
HAL_StatusTypeDef Send_Data_To_GM20(const char *data_string)
{
    uint16_t frameNo = 0;
    uint16_t data_count = 0;

    uint16_t data_length = strlen(data_string);

    printf("Sending to GM20: %s\r\n", data_string);

    // Send complete data packet to GM20 storage
    GM20_StatusTypeDef result = GM20_SendStringData((char*)data_string, data_length, &frameNo);

    if (result == GM20_OK) {
//        printf("Data sent successfully\r\n");

        // Query pending data count
        GM20_StatusTypeDef query_result = GM20_QueryDataCount(&data_count);
        if (query_result == GM20_OK) {
            printf("Pending data count: %d\r\n", data_count);
        }

        return HAL_OK;
    } else {
        printf("Data send failed\r\n");
        return HAL_ERROR;
    }
}

/* USER CODE END 0 */

/**
  * @brief  The application entry point.
  * @retval int
  */
int main(void)
{
  /* USER CODE BEGIN 1 */

  /* USER CODE END 1 */

  /* MCU Configuration--------------------------------------------------------*/

  /* Reset of all peripherals, Initializes the Flash interface and the Systick. */
  HAL_Init();

  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* Configure the system clock */
  SystemClock_Config();

  /* USER CODE BEGIN SysInit */

  /* USER CODE END SysInit */

  /* Initialize all configured peripherals */
  MX_GPIO_Init();
  MX_DMA_Init();
  MX_LPUART1_UART_Init();
  MX_USART1_UART_Init();
  MX_ADC_Init();
  MX_I2C1_Init();
  MX_RTC_Init();
  MX_SPI1_Init();
  /* USER CODE BEGIN 2 */

  // 初始化完成后，启动ADC DMA并打印数据
  printf("\r\n系统初始化完成\r\n");
  printf("开始ADC测试...\r\n");

  // 执行ADC测试
  ADC_Test_And_Print();

  /* USER CODE END 2 */

  /* Call init function for freertos objects (in freertos.c) */
  MX_FREERTOS_Init();

  /* Start scheduler */
  osKernelStart();

  /* We should never get here as control is now taken by the scheduler */
  /* Infinite loop */
  /* USER CODE BEGIN WHILE */
  while (1)
  {
    /* USER CODE END WHILE */

    /* USER CODE BEGIN 3 */
  }
  /* USER CODE END 3 */
}
